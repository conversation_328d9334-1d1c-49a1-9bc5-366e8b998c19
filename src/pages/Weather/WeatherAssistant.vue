<template>
  <main class="weather-main theme-background">
    <div class="weather-page">
      <!-- 顶部导航栏 -->
      <div class="top-bar-wrapper">
        <!-- 使用MaintopBar组件替代HeaderBar -->
        <Maintopbar
          :show-back-btn="false"
          :show-history-btn="false"
          :show-relationship-btn="false"
          :show-user-avatar="false"
          :show-assistant-avatar="true"
          :assistant-avatar-src="weatherIcon"
          :assistant-name="'董天气'"
          :selected-assistant-avatar="weatherIcon"
          :show-voice-btn="false"
          :show-add-chat-btn="false"
          :show-weather-btn="false"
          :show-memo-btn="false"
          :show-home-btn="false"
          :show-back-to-index-btn="true"
          :show-feature-intro="true"
          add-chat-type="add"
          :is-chat-play="false"
          :user-loading="false"
          :current-mis-id="''"
          :get-random-color="() => '#ccc'"
          :get-avatar-letter="() => ''"
          :show-header-grad="false"
          @back-to-index="onBackToIndex"
        />
      </div>

      <!-- 可滚动的内容区域 -->
      <div class="weather-container">
        <div class="content-area">
          <!-- 备忘录区（模块化） -->
          <MemoSection />

          <!-- GPS 天气卡片（模块化，复用当前城市结果做占位） -->
          <GPSWeatherCard :weather="current" :loading="loading" :error="error" />

          <!-- 划重点（仅在视图内部展开时显示“天气知识”） -->
          <WeatherNotablesView
            :groups="groupedNotables"
            :loading="loadingNotables"
            :loading-steps="notablesSteps"
            empty-reason="暂无关注城市，请在上方备忘录添加城市关键词（如：北京、上海）"
          />

          <!-- AI 深解读（对齐 iOS：加载步骤/多城市卡片/单条文本） -->
          <InterpretationSection
            :loading="aiLoading"
            :loading-finished="aiFinished"
            :loading-steps="aiSteps"
            :city-interpretations="aiCities"
            :interpretation-text="aiText"
          />
        </div>
      </div>

      <!-- 底部输入框 -->
      <div class="footer">
        <!-- 输入框 -->
        <form class="input-wrapper" action="" @submit.prevent="handleFormSubmit">
          <inputBar
            ref="inputBarRef"
            @voice-send="handleInputSend"
            @get-started="handleGetStarted"
            @send="handleInputSend"
            @stop="handleStop"
            @recording-status="handleRecordingStatus"
          />
        </form>

        <!-- 老董假装说话样式 - 直接放在输入框下方 -->
        <div class="laodong-fake-speaking">
          <div class="fake-speaking-container">
            <div class="laodong-avatar">
              <img :src="weatherIcon" alt="董天气头像" />
            </div>
            <div class="fake-speaking-content">
              <div class="fake-speaking-text">董天气会根据您的问题给出专业建议</div>
              <div class="fake-speaking-dots">
                <span class="dot"></span>
                <span class="dot"></span>
                <span class="dot"></span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <ButlerFloat
        v-if="butlerFloatVisible"
        :message="butlerTip"
        @click="onButlerFloatClick"
      />
    </div>
  </main>
</template>

<script setup lang="ts">
// iOS 天气服务对齐的适配层 + 头部组件
import { ref, onMounted, watch, computed, onBeforeUnmount } from 'vue'
import '@/styles/theme.css'
import Maintopbar from '@/components/Maintopbar.vue'
import MemoSection from '@/components/weather/MemoSection.vue'
// 旧 NotablesCard 保留在其他页面备用，这里改用新的视图组件三件套
import WeatherNotablesView from '@/components/notables/WeatherNotablesView.vue'
import InterpretationSection from '@/components/weather/InterpretationSection.vue'
import GPSWeatherCard from '@/components/weather/GPSWeatherCard.vue'
// Butler 组件
import ButlerFloat from '@/components/butler/ButlerFloat.vue'
import { butlerStore } from '@/stores/butlerStore'
// InputBar 组件
import inputBar from '@/components/Chat/inputBar.vue'
import { getLocationIdForCity, fetchCurrentWeather, fetchCurrentWeatherByCoord, type CurrentWeather } from '@/adapters/weatherAdapter'
import { WeatherService } from '@/services/weather/WeatherService'
import { WeatherAIInterpretationCoordinator } from '@/services/weather/WeatherAIInterpretationCoordinator'
import { streamWeatherPrompt, runWeatherPrompt } from '@/adapters/aiAdapter'
import { WeatherNotableService } from '@/services/weather/WeatherNotableService'
import { extractCitiesFromMemos } from '@/services/weather/MemoLocationExtractor'
import { memoStore } from '@/stores/memoStore'
import { useRouter } from 'vue-router'
import weatherIcon from '@/assets/assistant/dongtianqi.png'

const router = useRouter()
const city = ref('北京')
const loading = ref(false)
const error = ref('')
const current = ref<CurrentWeather | null>(null)

// InputBar 相关状态
const inputBarRef = ref<InstanceType<typeof inputBar> | null>(null)
const isRecording = ref(false)
const loadingNotables = ref(false)
const notablesSteps = ref<string[]>([])
const groupedNotables = ref<Array<{ city: string; notices: string[] }>>([])

// 服务实例（简单单例就地使用）
const weatherService = new WeatherService()
const notableService = new WeatherNotableService()
const aiCoordinator = new WeatherAIInterpretationCoordinator()

// —— 代理计算属性：将 ref 暴露为普通值，便于传入子组件 props（满足 TS 校验）
const aiLoading = computed(() => aiCoordinator.isLoading.value)
const aiFinished = computed(() => aiCoordinator.loadingState.value.isFinished)
const aiSteps = computed(() => aiCoordinator.loadingState.value.steps)
const aiCities = computed(() => aiCoordinator.cityInterpretations.value)
const aiText = computed(() => aiCoordinator.weatherInterpretation.value)
// 已移除调试日志面板

function handleNoCities() {
  // 清空服务与界面状态（对齐 iOS：当无关注城市时，模块展示空态）
  weatherService.clear()
  notableService.clear()
  aiCoordinator.reset()
  groupedNotables.value = []
  notablesSteps.value = []
}

const onQuery = async () => {
  error.value = ''
  current.value = null
  loading.value = true
  try {
    // 优先尝试 GPS（与 iOS 当前地点一致）
    const byGPS = await tryQueryByGPS()
    if (byGPS) return

    const id = getLocationIdForCity(city.value)
    if (!id) {
      error.value = '暂不支持该城市，请输入：北京/上海/广州/深圳（后续接入POI）'
      return
    }
    const cw = await fetchCurrentWeather(id)
    if (!cw) {
      error.value = '未获取到当前天气'
      return
    }
    current.value = cw

    // 刷新划重点：若备忘录有城市则按其刷新；否则清空模块展示空态
    const memoCities = resolveCitiesFromMemos()
    if (memoCities && memoCities.length) {
      await refreshNotables(memoCities)
    } else {
      handleNoCities()
    }
  } catch (e: any) {
    error.value = e?.message || '查询失败，请稍后再试'
  } finally {
    loading.value = false
  }
}

async function tryQueryByGPS(): Promise<boolean> {
  if (!('geolocation' in navigator)) return false
  try {
    const pos = await new Promise<GeolocationPosition>((resolve, reject) => {
      navigator.geolocation.getCurrentPosition(resolve, reject, { enableHighAccuracy: true, timeout: 8000, maximumAge: 60000 })
    })
    const { latitude, longitude } = pos.coords
    const cw = await fetchCurrentWeatherByCoord(latitude, longitude)
    if (!cw) {
      error.value = '未获取到当前地点天气'
      return true
    }
    current.value = cw
    // GPS 命中后依然严格以备忘录城市为准；无城市则显示空态
    const memoCities = resolveCitiesFromMemos()
    if (memoCities && memoCities.length) {
      await refreshNotables(memoCities)
    } else {
      handleNoCities()
    }
    return true
  } catch (err: any) {
    // 用户拒绝/超时/失败，回退城市查询
    return false
  }
}



onMounted(() => {
  // 页面首次进入尝试 GPS
  onQuery()
  // 互斥显示：监听 Composer 容器是否在视区
  setupComposerObserver()
})

onBeforeUnmount(() => {
  if (observer) observer.disconnect()
})

function resolveCitiesFromMemos(): string[] | null {
  const texts = memoStore.list.map((m: any) => m.text).filter(Boolean)
  const cities = extractCitiesFromMemos(texts)
  return cities.length ? cities : null
}

async function refreshNotables(cities: string[]) {
  loadingNotables.value = true
  notablesSteps.value = ['正在分析重点城市']
  try {
    weatherService.setQueriedCities(cities)
    notablesSteps.value = [...notablesSteps.value, '正在抓取天气数据']
    await weatherService.fetchAllByCities(cities)
    notablesSteps.value = [...notablesSteps.value, '正在提炼关键信息']
    await notableService.refreshNotables(weatherService)
    groupedNotables.value = notableService.groupedNotices
    notablesSteps.value = [...notablesSteps.value, '完成：生成城市卡片']
    // 刷新 AI 深解读
    await refreshInterpretation(cities)
  } finally {
    loadingNotables.value = false
  }
}

// 监听备忘录变化，驱动划重点刷新
watch(
  () => memoStore.list.map((m: any) => m.text),
  async () => {
    const cities = resolveCitiesFromMemos()
    if (cities && cities.length) {
      await refreshNotables(cities)
    } else {
      handleNoCities()
    }
  },
  { deep: true }
)

async function refreshInterpretation(cities: string[]) {
  const texts = memoStore.list.map((m: any) => m.text).filter(Boolean)
  // 注入 AI 执行器（支持流式；如需非流式可改为 prompt => runWeatherPrompt(prompt)）
  // 记录当前环境变量（仅开发日志）
  try {
    // @ts-ignore
    const env: any = import.meta.env || {}
    aiCoordinator['debugLogs'].value = [
      ...aiCoordinator['debugLogs'].value,
      `[${new Date().toISOString()}] ENV VITE_AI_API_BASE=${env.VITE_AI_API_BASE || '(empty)'} VITE_AI_MODEL=${env.VITE_AI_MODEL || '(empty)'} hasKey=${env.VITE_AI_API_KEY ? 'yes' : 'no'}`,
    ]
  } catch {}
  await aiCoordinator.generateWeatherInterpretation(
    cities,
    weatherService,
    texts,
    (prompt) => streamWeatherPrompt(prompt),
    {
      currentDate: new Date().toISOString(),
      currentLocationInfo: `当前查询城市：${city.value}`,
      locationAnalysis: '基于备忘录抽取的关注城市集合',
      // 若后续提供自然语言化的天气摘要，可注入 weatherDataNL 以完全对齐 iOS 模板
    }
  )
}

// —— Butler 互斥显示逻辑 ——
const composerContainer = ref<HTMLElement | null>(null)
let observer: IntersectionObserver | null = null
const butlerFloatVisible = computed(() => butlerStore.floatVisible.value)
const butlerTip = computed(() => butlerStore.tip.value || '需要我帮你组织一个回答吗？点我回到底部输入')

function setupComposerObserver() {
  if (!('IntersectionObserver' in window)) return
  if (observer) observer.disconnect()
  observer = new IntersectionObserver((entries) => {
    const e = entries[0]
    if (!e) return
    if (e.isIntersecting) {
      butlerStore.showComposer()
    } else {
      butlerStore.showFloat()
    }
  }, { threshold: 0.2 })
  if (composerContainer.value) observer.observe(composerContainer.value)
}

function onButlerFloatClick() {
  // 回到底部并显示 Composer
  butlerStore.showComposer()
  window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' })
}


function onBackToIndex() {
  // 返回首页 - 跳转到index.vue页面
  router.push({ name: 'chat' })
}

function onOpenHub() {
  // 占位：返回“助手大厅”页。若项目使用 vue-router，这里可以改为：router.push({ name: 'AssistantsHub' })
  alert('查看更多助手')
}

// InputBar 相关处理函数
const handleFormSubmit = () => {
  // 表单提交时不做任何操作，因为实际的发送逻辑由inputBar组件的send事件处理
  console.log('表单提交被阻止，使用组件事件处理')
}

const handleInputSend = (message: string) => {
  console.log('🚀 [WeatherAssistant] 发送消息:', message)
  // TODO: 实现天气助手的消息处理逻辑
}

const handleGetStarted = () => {
  console.log('🚀 [WeatherAssistant] 开始体验')
  // TODO: 实现开始体验逻辑
}

const handleStop = () => {
  console.log('🛑 [WeatherAssistant] 停止操作')
  // TODO: 实现停止逻辑
}

const handleRecordingStatus = (recording: boolean) => {
  isRecording.value = recording
  console.log('🎤 [WeatherAssistant] 录音状态变化:', recording)
}
</script>

<style scoped>
.weather-main {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

.weather-page {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  /* 预留底部空间，避免被绝对定位的输入框遮挡 */
  --footer-height: 160px;
  padding-bottom: calc(var(--footer-height, 160px) + env(safe-area-inset-bottom, 0px));
}

.top-bar-wrapper {
  flex-shrink: 0;
  position: sticky;
  top: 0;
  z-index: 100;
  background: var(--bg-primary);
}

.weather-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 800px;
  margin: 0 auto;
  width: 100%;
  padding: 0 var(--spacing-lg);
  box-sizing: border-box;
  overflow-y: auto;
  overflow-x: hidden;
}

.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
  padding: var(--spacing-xl) 0;
  min-height: 0;
}

/* 底部输入框样式 - 在页面内部固定在底部 */
.footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1100; /* 设置比弹窗更高的z-index，确保inputBar始终在最上层 */
  /* 不需要额外的padding-bottom，因为inputBar组件内部已经处理了安全区域适配 */
  /* inputBar组件使用了 calc(env(safe-area-inset-bottom) + 36px) 来处理底部安全区域 */

  .input-wrapper {
    width: 100%;
    padding: 0px;
    background: transparent;
  }
}

/* 老董假装说话样式 - 作为输入框的下半部分 */
.laodong-fake-speaking {
  padding: 0px 20px 12px 20px; /* 与输入框内部padding一致 */
  display: flex;
  justify-content: flex-start;
  /* 透明背景，与inputBar保持一致 */
  background: transparent;
  border: 2px solid var(--border-accent); /* 添加左右边框 */
  border-top: none; /* 去掉上边框 */
  border-radius: 0 0 20px 20px; /* 只有下方圆角，与输入框形成整体 */
  margin-top: -2px; /* 与输入框无缝连接 */
  /* 移除模糊效果 */

  .fake-speaking-container {
    display: flex;
    align-items: center; /* 改为居中对齐，一行显示 */
    gap: 12px;
    width: 100%; /* 占满宽度 */

    .laodong-avatar {
      width: 80px; /* 与inputBar中user-avatar保持一致 */
      height: 80px;
      border-radius: 50%;
      overflow: hidden;
      flex-shrink: 0;
      border: 2px solid var(--border-accent);
      box-shadow: var(--shadow-medium);

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .fake-speaking-content {
      display: flex;
      flex-direction: column;
      gap: 8px;
      flex: 1;

      .fake-speaking-text {
        color: var(--text-secondary);
        font-size: calc(var(--font-size-lg) + 4px); /* 增大字体 */
        line-height: 1.4;
        font-weight: 500;
      }

      .fake-speaking-dots {
        display: flex;
        gap: 4px;
        align-items: center;

        .dot {
          width: 6px;
          height: 6px;
          border-radius: 50%;
          background: var(--accent-color);
          animation: pulse 1.5s ease-in-out infinite;

          &:nth-child(1) {
            animation-delay: 0s;
          }
          &:nth-child(2) {
            animation-delay: 0.3s;
          }
          &:nth-child(3) {
            animation-delay: 0.6s;
          }
        }
      }
    }
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .weather-container {
    padding: 0 var(--spacing-md);
  }

  .content-area {
    gap: var(--spacing-lg);
    padding: var(--spacing-lg) 0;
  }

  .laodong-fake-speaking {
    padding: 10px 16px; /* 移动端保持背景padding */

    .fake-speaking-container {
      gap: 10px;

      .laodong-avatar {
        width: 64px; /* 与inputBar移动端user-avatar保持一致 */
        height: 64px;
      }

      .fake-speaking-content {
        gap: 6px;

        .fake-speaking-text {
          font-size: calc(var(--font-size-md) + 4px); /* 移动端也继续增大字体 */
        }

        .fake-speaking-dots {
          gap: 3px;

          .dot {
            width: 5px;
            height: 5px;
          }
        }
      }
    }
  }
}
</style>
